import os
import glob
import re
from PIL import Image

# https://github.com/Belval/pdf2image
from pdf2image import convert_from_path

# https://github.com/huggingface/transformers
from transformers import AutoTokenizer, AutoProcessor, AutoModelForImageTextToText


def pdf_to_images(pdf_path, local_image_dir):
    images = convert_from_path(pdf_path, dpi=150)
    images = [image.convert("RGB") for image in images]

    for i, image in enumerate(images):
        image_path = os.path.join(local_image_dir, f"page_{i + 1}.png")
        image.save(image_path)

def extract_page_number(filename):
    '''Trier les fichiers par ordre numérique des pages'''

    # Extraire le numéro de page du nom de fichier (ex: page_1.png -> 1)
    match = re.search(r'page_(\d+)\.png', os.path.basename(filename))
    return int(match.group(1)) if match else 0

def load_model():
    print("Loading Nanonets OCR model...")

    model_path = "nanonets/Nanonets-OCR-s"
    model = AutoModelForImageTextToText.from_pretrained(
        model_path,
        torch_dtype="auto",
        device_map="auto",
    )
    model.eval()

    processor = AutoProcessor.from_pretrained(model_path, use_fast=True)
    tokenizer = AutoTokenizer.from_pretrained(model_path)

    return model, processor, tokenizer


def ocr_page_with_nanonets_s(image_path, model, processor, max_new_tokens=4096):
    user_prompt = """Extract the text from the above document as if you were reading it naturally. Return the tables in mardown format. Return the equations in LaTeX representation. If there is an image in the document and image caption is not present, add a small description in french of the image inside the <img></img> tag; otherwise, add the image caption inside <img></img>. Watermarks should be wrapped in brackets. Ex: <watermark>OFFICIAL COPY</watermark>. Page numbers should be wrapped in brackets. Ex: <page_number>14</page_number> or <page_number>9/22</page_number>. Prefer using ☐ and ☑ for check boxes."""

    image = Image.open(image_path)
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {
            "role": "user",
            "content": [
                {"type": "image", "image": f"file://{image_path}"},
                {"type": "text", "text": user_prompt},
            ],
        },
    ]
    text = processor.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True
    )
    inputs = processor(text=[text], images=[image], padding=True, return_tensors="pt")
    inputs = inputs.to(model.device)

    output_ids = model.generate(
        **inputs, max_new_tokens=max_new_tokens, do_sample=False
    )
    generated_ids = [
        output_ids[len(input_ids) :]
        for input_ids, output_ids in zip(inputs.input_ids, output_ids)
    ]

    output_text = processor.batch_decode(
        generated_ids, skip_special_tokens=True, clean_up_tokenization_spaces=True
    )

    return output_text


def main():
    pdf_path = "trombinoscope.pdf"

    name_without_suff = ".".join(os.path.basename(pdf_path).split(".")[:-1])
    script_dir = os.path.dirname(os.path.abspath(__file__))
    local_md_dir = os.path.join(script_dir, "output", name_without_suff)
    local_image_dir = os.path.join(local_md_dir, "images")

    os.makedirs(local_image_dir, exist_ok=True)

    # pdf_to_images(pdf_path, local_image_dir)

    model, processor, _ = load_model()

    image_files = glob.glob(os.path.join(local_image_dir, "*.png"))
    if not image_files:
        print(f"Aucune image trouvée dans le répertoire {local_image_dir}")
        return
    image_files.sort(key=extract_page_number)


    print(f"Traitement de {len(image_files)} images...")

    concatenated_results = ""

    for i, image_path in enumerate(image_files):
        print(
            f"Traitement de l'image {i + 1}/{len(image_files)}: {os.path.basename(image_path)}"
        )

        result = ocr_page_with_nanonets_s(
            image_path, model, processor, max_new_tokens=15000
        )

        if i > 0:
            concatenated_results += "\n\n---\n\n"

        concatenated_results += result[0]

    output_md_path = os.path.join(
        local_md_dir, f"nanonets_{name_without_suff}.md"
    )
    with open(output_md_path, "w", encoding="utf-8") as f:
        f.write(concatenated_results)
    print(f"Résultat concaténé sauvegardé dans {output_md_path}")


if __name__ == "__main__":
    main()
